CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cgl_drl`(
    IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20),
    IN p_csrq VARCHAR(20),
    IN p_tz VARCHAR(20)
)
    COMMENT '单次常规用量分析存储过程'
main_block: BEGIN
		-- 声明变量
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs INT DEFAULT 0;
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_nl VARCHAR(20);
		DECLARE v_condition_id VARCHAR(20);
		DECLARE v_count_type VARCHAR(20);
		DECLARE v_n_count INT DEFAULT 0;
		DECLARE v_n_count1 INT DEFAULT 0;
		DECLARE v_yysm VARCHAR(50);
		DECLARE v_zx_flag VARCHAR(50);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_freq_times VARCHAR(10);
		DECLARE v_zongzhuanheng VARCHAR(200);
		
		-- 声明异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;
				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 获取药品基本信息
		SELECT DRUG_NAME, ZX_FLAG INTO v_ywa_name, v_zx_flag
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		-- 如果是中药则返回
		IF v_zx_flag = '3' THEN
				LEAVE main_block;
		END IF;

		-- 获取用药说明
		SELECT yysm INTO v_yysm
		FROM rms_t_pres_med 
		WHERE Code = p_code LIMIT 1;

		-- 如果用药说明包含"预防"且给药频次是"01"则返回
		IF v_yysm LIKE '%预防%' AND p_gypc = '01' THEN
				LEAVE main_block;
		END IF;

		-- 检查是否有医院自定义频次
		SELECT COUNT(1) INTO v_n_count
		FROM rms_t_med_zdy_pc  
		WHERE yp_code = p_yp_code;
		
		IF v_n_count > 0 THEN
				-- 检查频次是否符合自定义要求
				SELECT COUNT(1) INTO v_n_count1
				FROM rms_t_med_zdy_pc  
				WHERE yp_code = p_yp_code AND freq_code = p_gypc;
				
				IF v_n_count1 <= 0 THEN
						-- 获取自定义频次信息（这里简化处理，实际应该实现get_zongzhuanheng函数）
						SET v_zongzhuanheng = rms_get_zongzhuanheng(p_yp_code);
						
						-- 插入频次不符合提示
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						SELECT null, p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC', 
								'药品用法用量', CONCAT(v_ywa_name, '++++', '频次不符合医院自定义频次'),
								CONCAT('医院自定义要求该药频次为：', IFNULL(v_zongzhuanheng, '')), 
								0, '单次常规用量分析';
				ELSE
						-- 符合自定义频次要求，直接返回
						LEAVE main_block;
				END IF;
		END IF;

		-- 如果没有自定义频次，进行常规分析
		IF v_n_count = 0 THEN
				-- 获取SDA ID
				SELECT sda_id INTO v_sda_id
				FROM rms_t_byyydzb  
				WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;

				-- 获取给药途径编码
				SELECT DISTINCT SUBSTRING(by_code, 1, 2) INTO v_sda_tj
				FROM rms_t_tjdzb 
				WHERE h_tj = p_yp_tj LIMIT 1;

				-- 获取每日次数
				SELECT daily_times INTO v_sda_drcs
				FROM rms_itf_hos_frequency 
				WHERE freq_code = p_gypc LIMIT 1;

				-- 计算年龄（天数）
				SET v_nl = DATEDIFF(CURDATE(), STR_TO_DATE(p_csrq, '%Y%m%d'));

				-- 获取条件ID和计算类型
				SELECT id, count_type INTO v_condition_id, v_count_type
				FROM rms_t_sda_cgl_condition
				WHERE sda_id = v_sda_id
				AND admin_routine LIKE CONCAT('%', v_sda_tj, '%')
				AND age_min < v_nl
				AND age_max > v_nl
				LIMIT 1;

				-- 进行单次超极量分析
				IF v_condition_id IS NOT NULL THEN
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						SELECT p_code, v_ywa_name, '', '1', '一般提示', 'RLT030', 'YHGXHCGYFYLWT_PC', 
								'药品用法用量', 
								CONCAT(c.tymc, '++++', '频次不符合药品说明书推荐频次'),
								CONCAT('说明书提示：', c.tymc, '++++', '频次为：', 
												CAST(a.yl_min AS CHAR), '~', CAST(a.yl_max AS CHAR)),
								0, '单次常规用量分析' 
						FROM rms_t_sda c 
						LEFT JOIN rms_t_sda_cgl_result a ON a.sda_id = c.ID   
						WHERE c.ID = v_sda_id 
						AND a.condition_id = v_condition_id
						AND reco_type = '2'
						AND (a.yl_min > CAST(v_sda_drcs AS SIGNED) OR a.yl_max < CAST(v_sda_drcs AS SIGNED));
				END IF;
		END IF;

END